import 'dart:typed_data';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:photo_manager_image_provider/photo_manager_image_provider.dart';
import 'package:image_swiper/models/deleted_image.dart';
import 'package:image_swiper/utils/date_utils.dart';
import 'package:image_swiper/widgets/icon_text_button.dart';

// Custom animation curve for a more pronounced bounce effect
class CustomBounceCurve extends Curve {
  final double strength;

  const CustomBounceCurve({this.strength = 0.3});

  @override
  double transformInternal(double t) {
    // Ease out
    t = 1.0 - t;
    // Apply bounce effect
    return 1.0 -
        (math.pow(t, 2) * math.cos(t * 6 * strength * math.pi) * strength);
  }
}

class ImageGalleryScreen extends StatefulWidget {
  const ImageGalleryScreen({super.key});

  @override
  ImageGalleryScreenState createState() => ImageGalleryScreenState();
}

class ImageGalleryScreenState extends State<ImageGalleryScreen>
    with TickerProviderStateMixin {
  List<AssetEntity> _images = [];
  List<int> _imageIndexes = [];
  List<DeletedImage> _deletedImages = [];
  int _currentIndex = 0;
  final Map<int, Uint8List?> _imageCache = {};
  Uint8List? _currentImage;
  // New simple gesture state
  Offset _dragOffset = Offset.zero;
  bool _isDragging = false;
  bool _isAnimating = false;
  double _animationScale = 1.0;
  bool _isImageEntering = false;
  double _enterScale = 1.0;
  double _enterOpacity = 1.0;
  bool _isTransitioningFromBackground = false;
  double _backgroundTransitionProgress = 0.0;
  int? _targetImageIndex; // Store which image we're transitioning to
  AnimationController? _snapAnimationController;
  AnimationController? _enterAnimationController;
  AnimationController? _backgroundTransitionController;
  AnimationController? _deleteAnimationController;
  Animation<Offset>? _snapAnimation;
  Animation<double>? _scaleAnimation;
  Animation<double>? _enterScaleAnimation;
  Animation<double>? _enterOpacityAnimation;
  Animation<double>? _backgroundTransitionAnimation;

  @override
  void initState() {
    super.initState();
    _loadImages();

    // Initialize snap animation controller
    _initializeAnimationController();
  }

  void _initializeAnimationController() {
    _snapAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _enterAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _backgroundTransitionController = AnimationController(
      duration: const Duration(
        milliseconds: 300,
      ), // Same duration as snap animation
      vsync: this,
    );

    _deleteAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600), // Slower delete animation
      vsync: this,
    );

    _snapAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _snapAnimationController!,
        curve: Curves.easeOutCubic,
      ),
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _snapAnimationController!,
        curve: Curves.easeOutCubic,
      ),
    );

    _enterScaleAnimation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(
        parent: _enterAnimationController!,
        curve: Curves.easeOutBack,
      ),
    );

    _enterOpacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _enterAnimationController!,
        curve: Curves.easeOut,
      ),
    );

    _backgroundTransitionAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _backgroundTransitionController!,
        curve: Curves.easeOutCubic,
      ),
    );

    _snapAnimationController!.addListener(() {
      if (mounted) {
        setState(() {
          _dragOffset = _snapAnimation!.value;
          _animationScale = _scaleAnimation!.value;
        });
      }
    });

    _enterAnimationController!.addListener(() {
      if (mounted) {
        setState(() {
          _enterScale = _enterScaleAnimation!.value;
          _enterOpacity = _enterOpacityAnimation!.value;
        });
      }
    });

    _backgroundTransitionController!.addListener(() {
      if (mounted) {
        setState(() {
          _backgroundTransitionProgress = _backgroundTransitionAnimation!.value;
        });
      }
    });
  }

  @override
  void dispose() {
    _snapAnimationController?.dispose();
    _enterAnimationController?.dispose();
    _backgroundTransitionController?.dispose();
    _deleteAnimationController?.dispose();
    super.dispose();
  }

  Future<void> _loadImages() async {
    final PermissionState ps = await PhotoManager.requestPermissionExtend();
    if (ps.isAuth) {
      final List<AssetPathEntity> albums = await PhotoManager.getAssetPathList(
        onlyAll: true,
        type: RequestType.image,
      );

      if (albums.isNotEmpty) {
        final AssetPathEntity album = albums[0];
        final List<AssetEntity> photos = await album.getAssetListPaged(
          page: 0,
          size: 10000,
        );

        // Sort photos by creation date (newest first)
        photos.sort((a, b) => b.createDateTime.compareTo(a.createDateTime));

        setState(() {
          _images = photos;
          _imageIndexes = List.generate(_images.length, (index) => index);
        });
        _loadCurrentImage();
      }
    } else {
      print('Permission denied');
    }
  }

  Future<void> _loadCurrentImage() async {
    if (_imageIndexes.isEmpty) {
      return;
    }
    // With AssetEntityImage, we don't need to manually load image data
    // The widget handles loading internally
    if (mounted) {
      setState(() {
        // Just trigger a rebuild - the AssetEntityImage will handle loading
      });
    }
  }

  Future<Uint8List?> _getCachedOrFetchImageData(int index) async {
    if (_imageCache.containsKey(index)) {
      return _imageCache[index];
    }
    final data = await _fetchImageData(_images[index]);
    _imageCache[index] = data;
    return data;
  }

  Future<Uint8List?> _fetchImageData(AssetEntity asset) async {
    try {
      final data = await asset.thumbnailDataWithSize(
        const ThumbnailSize(800, 800),
        quality: 95,
      );
      return data;
    } catch (e) {
      print('Error fetching image: $e');
      return null;
    }
  }

  // New gesture handlers for simple drag system
  void _onPanStart(DragStartDetails details) {
    if (_imageIndexes.isEmpty) return;
    setState(() {
      _isDragging = true;
      _dragOffset = Offset.zero;
    });
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (!_isDragging) return;
    setState(() {
      _dragOffset = Offset(
        _dragOffset.dx + details.delta.dx,
        _dragOffset.dy + details.delta.dy,
      );
    });
  }

  void _onPanEnd(DragEndDetails details) {
    if (!_isDragging) return;

    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // Determine gesture type based on drag direction and distance
    final horizontalDistance = _dragOffset.dx.abs();
    final verticalDistance = _dragOffset.dy.abs();
    final threshold = 100.0; // Minimum distance to trigger action

    if (verticalDistance > horizontalDistance && _dragOffset.dy < -threshold) {
      // Drag up - animate to trash bin
      _animateToTrash();
    } else if (horizontalDistance > verticalDistance &&
        horizontalDistance > threshold) {
      if (_dragOffset.dx < 0 && _currentIndex < _imageIndexes.length - 1) {
        // Drag left - animate off screen left
        _animateOffScreenLeft();
      } else if (_dragOffset.dx > 0) {
        // Drag right - animate off screen right
        _animateOffScreenRight();
      } else {
        // Not enough distance or can't perform action - snap back
        _snapBackToCenter();
      }
    } else {
      // Not enough distance - snap back to center
      _snapBackToCenter();
    }
  }

  void _animateOffScreenLeft() {
    final screenWidth = MediaQuery.of(context).size.width;

    // Store the target image index (next image)
    final targetIndex = _currentIndex + 1;

    // Mark that we're transitioning from background and start the synchronized animation
    setState(() {
      _isTransitioningFromBackground = true;
      _backgroundTransitionProgress = 0.0;
      _targetImageIndex = targetIndex;
    });

    // Start background transition animation synchronized with exit animation
    _startBackgroundTransition();

    _animateToTarget(Offset(-screenWidth, _dragOffset.dy), () {
      // After animation, switch to next image
      if (mounted) {
        setState(() {
          _currentIndex++;
          _isDragging = false;
          _dragOffset = Offset.zero;
        });
        _loadCurrentImage().then((_) {
          // Background transition should be complete by now
          _finishBackgroundTransition();
        });
      }
    });
  }

  void _animateOffScreenRight() {
    final screenWidth = MediaQuery.of(context).size.width;

    // Drag right should only navigate to previous image, not restore deleted images
    if (_currentIndex > 0) {
      // Store the target image index (previous image)
      final targetIndex = _currentIndex - 1;

      // Mark that we're transitioning from background and start the synchronized animation
      setState(() {
        _isTransitioningFromBackground = true;
        _backgroundTransitionProgress = 0.0;
        _targetImageIndex = targetIndex;
      });

      // Start background transition animation synchronized with exit animation
      _startBackgroundTransition();

      // Animate off screen to the right
      _animateToTarget(Offset(screenWidth, _dragOffset.dy), () {
        // After animation, switch to previous image
        if (mounted) {
          setState(() {
            _currentIndex--;
            _isDragging = false;
            _dragOffset = Offset.zero;
          });
          _loadCurrentImage().then((_) {
            _finishBackgroundTransition();
          });
        }
      });
    } else {
      // Can't go back, just snap back to center
      _snapBackToCenter();
    }
  }

  void _animateToTrash() {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // Store the target image index (next image after deletion)
    int? targetIndex;
    if (_currentIndex < _imageIndexes.length - 1) {
      // Next image will become current
      targetIndex = _currentIndex + 1;
    } else if (_currentIndex > 0) {
      // Previous image will become current (deleting last image)
      targetIndex = _currentIndex - 1;
    }

    // Mark that we're transitioning from background and start the synchronized animation
    setState(() {
      _isTransitioningFromBackground = targetIndex != null;
      _backgroundTransitionProgress = 0.0;
      _targetImageIndex = targetIndex;
    });

    // Start background transition animation synchronized with delete animation
    if (targetIndex != null) {
      _startBackgroundTransition(isDeleteAnimation: true);
    }

    // Move to top-right corner: right edge and up significantly
    final topRightPosition = Offset(
      screenWidth, // Move to right edge
      -screenHeight * 0.5, // Move up significantly to reach top
    );

    _animateToTargetWithScaleSlowly(topRightPosition, 0.0, () {
      // After animation, delete the image
      if (mounted) {
        _deleteImage();
        setState(() {
          _isDragging = false;
          _dragOffset = Offset.zero;
        });
        // Background transition should be complete by now
        if (targetIndex != null) {
          _finishBackgroundTransition();
        }
      }
    });
  }

  void _snapBackToCenter() {
    _animateToTarget(Offset.zero, () {
      setState(() {
        _isDragging = false;
      });
    });
  }

  void _animateToTarget(Offset target, VoidCallback onComplete) {
    _animateToTargetWithScale(target, 1.0, onComplete);
  }

  void _animateToTargetWithScale(
    Offset target,
    double targetScale,
    VoidCallback onComplete,
  ) {
    // Ensure animation controller is initialized
    if (_snapAnimationController == null) {
      _initializeAnimationController();
    }

    setState(() {
      _isAnimating = true;
    });

    _snapAnimation = Tween<Offset>(begin: _dragOffset, end: target).animate(
      CurvedAnimation(
        parent: _snapAnimationController!,
        curve: Curves.easeOutCubic,
      ),
    );

    _scaleAnimation = Tween<double>(
      begin: _animationScale,
      end: targetScale,
    ).animate(
      CurvedAnimation(
        parent: _snapAnimationController!,
        curve: Curves.easeOutCubic,
      ),
    );

    _snapAnimationController!.reset();
    _snapAnimationController!.forward().then((_) {
      setState(() {
        _isAnimating = false;
        _animationScale = 1.0; // Reset scale after animation
      });
      onComplete();
    });
  }

  void _animateToTargetWithScaleSlowly(
    Offset target,
    double targetScale,
    VoidCallback onComplete,
  ) {
    // Ensure animation controller is initialized
    if (_deleteAnimationController == null) {
      _initializeAnimationController();
    }

    setState(() {
      _isAnimating = true;
    });

    // Use delete animation controller for slower animation
    final deleteSnapAnimation = Tween<Offset>(
      begin: _dragOffset,
      end: target,
    ).animate(
      CurvedAnimation(
        parent: _deleteAnimationController!,
        curve: Curves.easeOutQuart, // Smoother curve for delete
      ),
    );

    final deleteScaleAnimation = Tween<double>(
      begin: _animationScale,
      end: targetScale,
    ).animate(
      CurvedAnimation(
        parent: _deleteAnimationController!,
        curve: Curves.easeOutQuart,
      ),
    );

    // Add listener for delete animation
    void deleteListener() {
      if (mounted) {
        setState(() {
          _dragOffset = deleteSnapAnimation.value;
          _animationScale = deleteScaleAnimation.value;
        });
      }
    }

    _deleteAnimationController!.addListener(deleteListener);

    _deleteAnimationController!.reset();
    _deleteAnimationController!.forward().then((_) {
      _deleteAnimationController!.removeListener(deleteListener);
      setState(() {
        _isAnimating = false;
        _animationScale = 1.0; // Reset scale after animation
      });
      onComplete();
    });
  }

  void _startBackgroundTransition({bool isDeleteAnimation = false}) {
    // Ensure animation controller is initialized
    if (_backgroundTransitionController == null) {
      _initializeAnimationController();
    }

    // Adjust duration for delete animations
    if (isDeleteAnimation) {
      _backgroundTransitionController!.duration = const Duration(
        milliseconds: 600,
      );
    } else {
      _backgroundTransitionController!.duration = const Duration(
        milliseconds: 300,
      );
    }

    // Start the background transition animation
    _backgroundTransitionController!.reset();
    _backgroundTransitionController!.forward();
  }

  void _finishBackgroundTransition() {
    // Clean up transition state
    if (mounted) {
      setState(() {
        _isTransitioningFromBackground = false;
        _backgroundTransitionProgress = 0.0;
        _targetImageIndex = null;
      });
    }
  }

  void _startEnterAnimation() {
    // Ensure animation controller is initialized
    if (_enterAnimationController == null) {
      _initializeAnimationController();
    }

    setState(() {
      _isImageEntering = true;
      _enterScale = 0.3;
      _enterOpacity = 0.0;
    });

    _enterAnimationController!.reset();
    _enterAnimationController!.forward().then((_) {
      setState(() {
        _isImageEntering = false;
        _enterScale = 1.0;
        _enterOpacity = 1.0;
      });
    });
  }

  void _deleteImageWithAnimation() {
    if (_imageIndexes.isEmpty) return;

    // Use the same animation as up swipe gesture
    _animateToTrash();
  }

  void _deleteImage() {
    if (_imageIndexes.isEmpty) return;
    setState(() {
      _deletedImages.add(
        DeletedImage(
          index: _imageIndexes[_currentIndex],
          originalPosition: _currentIndex,
        ),
      );
      _imageIndexes.removeAt(_currentIndex);
      if (_imageIndexes.isEmpty) {
        _currentIndex = 0;
      } else if (_currentIndex >= _imageIndexes.length) {
        _currentIndex = _imageIndexes.length - 1;
      }
      _loadCurrentImage();
    });
  }

  void _undoDeleteImage() {
    if (_deletedImages.isEmpty) return;
    final lastDeleted = _deletedImages.removeLast();
    final int originalIndex = lastDeleted.index;
    int targetPosition = lastDeleted.originalPosition;
    if (targetPosition > _imageIndexes.length) {
      targetPosition = _imageIndexes.length;
    }
    setState(() {
      _imageIndexes.insert(targetPosition, originalIndex);
      _currentIndex = targetPosition;
      _loadCurrentImage();
    });
  }

  void _undoAction() {
    if (_deletedImages.isNotEmpty) {
      _undoDeleteImage();
    }
  }

  Future<void> _clearTrash() async {
    if (_deletedImages.isEmpty) return;
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Clear Trash'),
          content: Text(
            'Are you sure you want to permanently delete ${_deletedImages.length} image${_deletedImages.length == 1 ? '' : 's'}?',
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () => Navigator.of(context).pop(false),
            ),
            TextButton(
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
              onPressed: () => Navigator.of(context).pop(true),
            ),
          ],
        );
      },
    );

    if (confirm == true) {
      try {
        final List<String> idsToDelete =
            _deletedImages.map((deleted) => _images[deleted.index].id).toList();
        await PhotoManager.editor.deleteWithIds(idsToDelete);
        for (final deleted in _deletedImages) {
          _imageCache.remove(deleted.index);
        }
        setState(() {
          _deletedImages.clear();
        });
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Images permanently deleted'),
            backgroundColor: Colors.green,
          ),
        );
      } catch (e) {
        print('Error deleting images: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to delete images'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _goToNextImage() {
    if (!_canGoNext()) return;

    // Use the same animation as left swipe gesture
    _animateOffScreenLeft();
  }

  double _getImageCenterY() {
    final screenHeight = MediaQuery.of(context).size.height;
    final statusBarHeight = MediaQuery.of(context).padding.top;
    final bottomSafeArea = MediaQuery.of(context).padding.bottom;

    // Calculate the height taken by other UI elements
    final headerHeight = 56.0;
    final imageInfoHeight =
        80.0; // Estimated height for image info section with padding
    final bottomButtonsHeight =
        100.0; // Estimated height for bottom buttons section

    // Calculate the available height for the image viewer
    final imageViewerHeight =
        screenHeight -
        statusBarHeight -
        headerHeight -
        imageInfoHeight -
        bottomButtonsHeight -
        bottomSafeArea;

    // Return the center of the image viewer area
    return imageViewerHeight / 2;
  }

  @override
  Widget build(BuildContext context) {
    final statusBarHeight = MediaQuery.of(context).padding.top;

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          Column(
            children: [
              SizedBox(height: statusBarHeight),

              SizedBox(
                height: 56,
                child: Stack(
                  children: [
                    Positioned(
                      top: 0,
                      right: 16,
                      child: Container(
                        width: 56,
                        height: 56,
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            IconButton(
                              icon: const Icon(
                                Icons.delete_outline,
                                color: Colors.white,
                                size: 28,
                              ),
                              onPressed:
                                  _deletedImages.isEmpty ? null : _clearTrash,
                              padding: const EdgeInsets.all(12.0),
                              constraints: const BoxConstraints(
                                minWidth: 56,
                                minHeight: 56,
                              ),
                            ),
                            if (_deletedImages.isNotEmpty)
                              Positioned(
                                right: 4,
                                top: 4,
                                child: Container(
                                  padding: const EdgeInsets.all(2),
                                  decoration: BoxDecoration(
                                    color: Colors.red,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  constraints: const BoxConstraints(
                                    minWidth: 16,
                                    minHeight: 16,
                                  ),
                                  child: Text(
                                    _deletedImages.length.toString(),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 10,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              if (_imageIndexes.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                  child: Column(
                    children: [
                      Text(
                        'Image ${_currentIndex + 1} of ${_imageIndexes.length}',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Taken on ${formatDate(getImageDate(_getCurrentImageAsset()))}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),

              Expanded(
                child:
                    _imageIndexes.isEmpty
                        ? const Center(
                          child: Text(
                            'No images found',
                            style: TextStyle(color: Colors.white),
                          ),
                        )
                        : _buildImageViewerContent(),
              ),

              SafeArea(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 24.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      IconTextButton(
                        icon: Icons.arrow_forward,
                        label: 'Next',
                        onPressed: _canGoNext() ? () => _goToNextImage() : null,
                      ),
                      IconTextButton(
                        icon: Icons.undo,
                        label: 'Undo',
                        onPressed: _canUndo() ? _undoAction : null,
                      ),
                      IconTextButton(
                        icon: Icons.delete,
                        label: 'Delete',
                        onPressed:
                            _imageIndexes.isEmpty
                                ? null
                                : _deleteImageWithAnimation,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),

          _buildIndicatorsLayer(),
        ],
      ),
    );
  }

  Widget _buildIndicatorsLayer() {
    return Positioned.fill(
      child: Stack(
        children: [
          // Indicators will be simplified for new gesture system
        ],
      ),
    );
  }

  Widget _buildImageViewerContent() {
    if (_imageIndexes.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    return GestureDetector(
      onPanStart: _onPanStart,
      onPanUpdate: _onPanUpdate,
      onPanEnd: _onPanEnd,
      child: Stack(
        fit: StackFit.expand,
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.black,
              gradient: RadialGradient(
                center: Alignment.center,
                radius: 1.5,
                colors: [Colors.black.withValues(alpha: 0.7), Colors.black],
                stops: const [0.0, 1.0],
              ),
            ),
          ),
          // Background image that appears during dragging and transitions
          if (_isDragging || _isTransitioningFromBackground)
            _buildBackgroundImage(),
          // Current image display with drag feedback
          _buildCurrentImage(),
          // Visual feedback for gestures
          if (_isDragging) _buildGestureIndicators(),
        ],
      ),
    );
  }

  Widget _buildCurrentImage() {
    if (_imageIndexes.isEmpty) return Container();
    final currentAsset = _images[_imageIndexes[_currentIndex]];

    if (_isDragging || _isAnimating || _isImageEntering) {
      // Simplified dragging transformation - just follow the drag offset
      double scale =
          _isAnimating
              ? _animationScale
              : (_isImageEntering ? _enterScale : 1.0);
      double opacity = _isImageEntering ? _enterOpacity : 1.0;
      double rotation = 0.0;

      // No conditional logic during dragging - keep it simple
      // All action decisions happen in _onPanEnd()

      return Positioned.fill(
        child: Transform.translate(
          offset: _dragOffset,
          child: Transform.scale(
            scale: scale,
            child: Transform.rotate(
              angle: rotation,
              child: Opacity(
                opacity: opacity,
                child: _buildAssetImageWidget(currentAsset),
              ),
            ),
          ),
        ),
      );
    } else {
      return Positioned.fill(child: _buildAssetImageWidget(currentAsset));
    }
  }

  Widget _buildBackgroundImage() {
    if (_imageIndexes.isEmpty) return Container();

    // During transition, smoothly animate the target image to full visibility
    if (_isTransitioningFromBackground && _targetImageIndex != null) {
      final targetAsset = _images[_imageIndexes[_targetImageIndex!]];

      // Calculate current drag progress for smooth transition
      final horizontalDistance = _dragOffset.dx.abs();
      final screenWidth = MediaQuery.of(context).size.width;
      final dragProgress = (horizontalDistance / screenWidth).clamp(0.0, 1.0);

      // Interpolate between drag state and final state using transition progress
      final startOpacity = dragProgress * 0.9;
      final startScale = 0.7 + (dragProgress * 0.3);

      final finalOpacity = 1.0;
      final finalScale = 1.0;

      final currentOpacity =
          startOpacity +
          (_backgroundTransitionProgress * (finalOpacity - startOpacity));
      final currentScale =
          startScale +
          (_backgroundTransitionProgress * (finalScale - startScale));

      return Positioned.fill(
        child: Opacity(
          opacity: currentOpacity,
          child: Transform.scale(
            scale: currentScale,
            child: _buildAssetImageWidget(targetAsset),
          ),
        ),
      );
    }

    final horizontalDistance = _dragOffset.dx.abs();
    final verticalDistance = _dragOffset.dy.abs();

    // Show background image for horizontal drags (navigation) OR vertical drags (delete)
    bool isHorizontalDrag = horizontalDistance > verticalDistance;
    bool isVerticalDrag =
        verticalDistance > horizontalDistance &&
        _dragOffset.dy < 0; // Only up drags

    if (!isHorizontalDrag && !isVerticalDrag) return Container();

    AssetEntity? backgroundAsset;
    double opacity = 0.0;
    double scale = 0.8;

    if (isHorizontalDrag) {
      // Calculate drag progress for horizontal navigation (0.0 to 1.0)
      final screenWidth = MediaQuery.of(context).size.width;
      final dragProgress = (horizontalDistance / screenWidth).clamp(0.0, 1.0);

      if (_dragOffset.dx < 0 && _currentIndex < _imageIndexes.length - 1) {
        // Dragging left - show next image
        backgroundAsset = _images[_imageIndexes[_currentIndex + 1]];
        opacity = dragProgress * 0.9; // Higher opacity for smoother transition
        scale =
            0.7 +
            (dragProgress * 0.3); // Larger scale range for smoother transition
      } else if (_dragOffset.dx > 0) {
        // Dragging right - show previous image only (not deleted images)
        if (_currentIndex > 0) {
          backgroundAsset = _images[_imageIndexes[_currentIndex - 1]];
        }

        if (backgroundAsset != null) {
          opacity = dragProgress * 0.9;
          scale = 0.7 + (dragProgress * 0.3);
        }
      }
    } else if (isVerticalDrag) {
      // Calculate drag progress for vertical delete (0.0 to 1.0)
      final screenHeight = MediaQuery.of(context).size.height;
      final dragProgress = (verticalDistance / (screenHeight * 0.3)).clamp(
        0.0,
        1.0,
      );

      // Determine which image will be shown after deletion
      if (_currentIndex < _imageIndexes.length - 1) {
        // Next image will become current
        backgroundAsset = _images[_imageIndexes[_currentIndex + 1]];
      } else if (_currentIndex > 0) {
        // Previous image will become current (deleting last image)
        backgroundAsset = _images[_imageIndexes[_currentIndex - 1]];
      }

      if (backgroundAsset != null) {
        opacity = dragProgress * 0.9;
        scale = 0.7 + (dragProgress * 0.3);
      }
    }

    if (backgroundAsset == null) return Container();

    return Positioned.fill(
      child: Opacity(
        opacity: opacity,
        child: Transform.scale(
          scale: scale,
          child: _buildAssetImageWidget(backgroundAsset),
        ),
      ),
    );
  }

  Widget _buildGestureIndicators() {
    final horizontalDistance = _dragOffset.dx.abs();
    final verticalDistance = _dragOffset.dy.abs();
    final threshold = 100.0;

    List<Widget> indicators = [];

    if (verticalDistance > horizontalDistance && _dragOffset.dy < -50) {
      // Show delete indicator
      final progress = (_dragOffset.dy.abs() / threshold).clamp(0.0, 1.0);
      indicators.add(
        Positioned(
          top: 100,
          left: 0,
          right: 0,
          child: Center(
            child: Column(
              children: [
                Icon(
                  Icons.delete_outline,
                  color: Colors.red.withValues(alpha: 0.5 + progress * 0.5),
                  size: 40 + progress * 20,
                ),
                const SizedBox(height: 8),
                Text(
                  progress >= 1.0 ? "Release to Delete" : "Drag Up to Delete",
                  style: TextStyle(
                    color: Colors.red.withValues(alpha: 0.5 + progress * 0.5),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    } else if (horizontalDistance > verticalDistance &&
        horizontalDistance > 50) {
      // Show navigation indicators
      final progress = (horizontalDistance / threshold).clamp(0.0, 1.0);

      if (_dragOffset.dx < 0 && _currentIndex < _imageIndexes.length - 1) {
        // Left drag - next image
        indicators.add(
          Positioned(
            right: 50,
            top: _getImageCenterY(),
            child: Icon(
              Icons.arrow_forward_ios,
              color: Colors.white.withValues(alpha: 0.5 + progress * 0.5),
              size: 30 + progress * 20,
            ),
          ),
        );
      } else if (_dragOffset.dx > 0) {
        // Right drag - show previous image indicator only
        if (_currentIndex > 0) {
          // Show previous image indicator
          indicators.add(
            Positioned(
              left: 50,
              top: _getImageCenterY(),
              child: Column(
                children: [
                  Icon(
                    Icons.arrow_back,
                    color: Colors.blue.withValues(alpha: 0.5 + progress * 0.5),
                    size: 30 + progress * 20,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    "Previous",
                    style: TextStyle(
                      color: Colors.blue.withValues(
                        alpha: 0.5 + progress * 0.5,
                      ),
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          );
        }
      }
    }

    return Stack(children: indicators);
  }

  Widget _buildAssetImageWidget(AssetEntity asset) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      alignment: Alignment.center,
      child: AssetEntityImage(
        asset,
        isOriginal: false,
        thumbnailSize: const ThumbnailSize.square(800),
        thumbnailFormat: ThumbnailFormat.jpeg,
        fit: BoxFit.contain,
        filterQuality: FilterQuality.high,
      ),
    );
  }

  bool _canGoNext() {
    return _imageIndexes.isNotEmpty && _currentIndex < _imageIndexes.length - 1;
  }

  bool _canUndo() {
    return _deletedImages.isNotEmpty;
  }

  AssetEntity _getCurrentImageAsset() {
    return _images[_imageIndexes[_currentIndex]];
  }
}
