import 'package:photo_manager/photo_manager.dart';

String formatDate(DateTime date) {
  if (date == DateTime(0)) return 'Unknown date';
  return '${date.day}/${date.month}/${date.year} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
}

DateTime getImageDate(AssetEntity image) {
  // Use createDateTime (when photo was taken) instead of modifiedDateTime
  // createDateTime is the actual date the photo was captured
  return image.createDateTime;
}

double easeInQuad(double x) {
  return x * x;
}
